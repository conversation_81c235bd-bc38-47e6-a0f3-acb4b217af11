<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Personal Portfolio</title>
    <script src="https://cdn.tailwindcss.com/3.4.16"></script>
    <link rel="icon" href="img/logo.png">
    <script>
      tailwind.config = {
        theme: {
          extend: {
            colors: { primary: "#15B8A6", secondary: "#10b981" },
            borderRadius: {
              none: "0px",
              sm: "4px",
              DEFAULT: "8px",
              md: "12px",
              lg: "16px",
              xl: "20px",
              "2xl": "24px",
              "3xl": "32px",
              full: "9999px",
              button: "8px",
            },
          },
        },
      };
    </script>
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link
      href="https://fonts.googleapis.com/css2?family=Pacifico&display=swap"
      rel="stylesheet"
    />
    <link
      href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap"
      rel="stylesheet"
    />
    <link
      rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/remixicon/4.6.0/remixicon.min.css"
    />
    <style>
      :where([class^="ri-"])::before { content: "\f3c2"; }
      body {
      font-family: 'Inter', sans-serif;
      }
      .scroll-down-arrow {
      animation: bounce 2s infinite;
      }
      @keyframes bounce {
      0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
      40% { transform: translateY(-10px); }
      60% { transform: translateY(-5px); }
      }
      .scroll-top-button {
      opacity: 0;
      visibility: hidden;
      transition: opacity 0.3s, visibility 0.3s;
      }
      .scroll-top-button.visible {
      opacity: 1;
      visibility: visible;
      }
      input:focus, button:focus {
      outline: none;
      }
      .animate-fade-up {
      opacity: 0;
      transform: translateY(30px);
      transition: opacity 0.6s ease-out, transform 0.6s ease-out;
      }
      .animate-fade-up.visible {
      opacity: 1;
      transform: translateY(0);
      }
      .animate-fade-in {
      opacity: 0;
      transition: opacity 0.6s ease-out;
      }
      .animate-fade-in.visible {
      opacity: 1;
      }
      .stagger-animation {
      transition-delay: calc(var(--animation-order) * 0.1s);
      }
      .scale-hover {
      transition: transform 0.3s ease;
      }
      .scale-hover:hover {
      transform: scale(1.02);
      }
      .nav-link {
      position: relative;
      }
      .nav-link::after {
      content: '';
      position: absolute;
      width: 0;
      height: 2px;
      bottom: -4px;
      left: 0;
      background-color: #15B8A6;
      transition: width 0.3s ease;
      }
      .nav-link:hover::after {
      width: 100%;
      }
      .progress-bar {
        position: fixed;
        top: 0;
        left: 0;
        height: 3px;
        background: #15B8A6;
        z-index: 9999;
        transition: width 0.1s ease;
      }
      @keyframes blink-caret {
      from, to { border-color: transparent }
      50% { border-color: #15B8A6 }
      }

      /* Enhanced Background Animations */
      @keyframes float {
        0%, 100% { transform: translateY(0px) rotate(0deg); }
        50% { transform: translateY(-20px) rotate(180deg); }
      }

      @keyframes floatReverse {
        0%, 100% { transform: translateY(0px) rotate(0deg); }
        50% { transform: translateY(20px) rotate(-180deg); }
      }

      @keyframes pulse {
        0%, 100% { opacity: 0.4; transform: scale(1); }
        50% { opacity: 0.8; transform: scale(1.1); }
      }

      @keyframes slideInLeft {
        0% { transform: translateX(-100px); opacity: 0; }
        100% { transform: translateX(0); opacity: 1; }
      }

      /* Background Elements */
      .bg-animated {
        position: relative;
        overflow: hidden;
      }

      .bg-animated::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(135deg,
          rgba(21, 184, 166, 0.1) 0%,
          rgba(16, 185, 129, 0.05) 25%,
          rgba(59, 130, 246, 0.05) 50%,
          rgba(139, 92, 246, 0.1) 75%,
          rgba(236, 72, 153, 0.05) 100%);
        z-index: 1;
      }

      .floating-element {
        position: absolute;
        border-radius: 50%;
        background: linear-gradient(45deg, rgba(21, 184, 166, 0.1), rgba(16, 185, 129, 0.2));
        backdrop-filter: blur(10px);
        z-index: 2;
      }

      .floating-element:nth-child(1) {
        width: 80px;
        height: 80px;
        top: 10%;
        left: 10%;
        animation: float 6s ease-in-out infinite;
      }

      .floating-element:nth-child(2) {
        width: 120px;
        height: 120px;
        top: 20%;
        right: 10%;
        animation: floatReverse 8s ease-in-out infinite;
        animation-delay: -2s;
      }

      .floating-element:nth-child(3) {
        width: 60px;
        height: 60px;
        bottom: 30%;
        left: 20%;
        animation: pulse 4s ease-in-out infinite;
        animation-delay: -1s;
      }

      .floating-element:nth-child(4) {
        width: 100px;
        height: 100px;
        bottom: 20%;
        right: 20%;
        animation: float 7s ease-in-out infinite;
        animation-delay: -3s;
      }

      /* Glassmorphism Effect */
      .glass-effect {
        background: rgba(255, 255, 255, 0.1);
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.2);
      }

      /* Enhanced Section Backgrounds */
      .section-pattern {
        background-image:
          radial-gradient(circle at 25% 25%, rgba(21, 184, 166, 0.1) 0%, transparent 50%),
          radial-gradient(circle at 75% 75%, rgba(16, 185, 129, 0.1) 0%, transparent 50%);
      }
    </style>
  </head>
  <body class="bg-gray-50 min-h-screen">
    <!-- Navigation -->
    <nav class="fixed top-0 left-0 w-full bg-white shadow-md z-50">
      <div class="container mx-auto px-6 md:px-12">
        <div class="flex justify-between items-center h-16">
          <!-- Logo -->
          <a href="#" class="text-2xl font-['Pacifico']" style="color: #15B8A6;"
            >iArcillas</a
          >
          <!-- Desktop Navigation -->
          <div class="hidden md:flex items-center space-x-8">
            <a
              href="#hero"
              class="text-gray-600 hover:text-primary transition-colors"
              >Home</a
            >
            <a
              href="#about"
              class="text-gray-600 hover:text-primary transition-colors"
              >About</a
            >
            <a
              href="#projects"
              class="text-gray-600 hover:text-primary transition-colors"
              >Projects</a
            >
            <a
              href="#skills"
              class="text-gray-600 hover:text-primary transition-colors"
              >Skills</a
            >
            <a
              href="#contact"
              class="bg-primary hover:bg-[#13a696] text-white px-4 py-2 !rounded-button transition-all duration-300"
              >Contact</a
            >
          </div>
          <!-- Mobile Menu Button -->
          <button
            id="mobileMenuBtn"
            class="md:hidden w-10 h-10 flex items-center justify-center text-gray-600"
          >
            <i class="ri-menu-line ri-lg"></i>
          </button>
        </div>
        <!-- Mobile Navigation -->
        <div id="mobileMenu" class="hidden md:hidden py-4">
          <div class="flex flex-col space-y-4">
            <a
              href="#hero"
              class="text-gray-600 hover:text-primary transition-colors"
              >Home</a
            >
            <a
              href="#about"
              class="text-gray-600 hover:text-primary transition-colors"
              >About</a
            >
            <a
              href="#projects"
              class="text-gray-600 hover:text-primary transition-colors"
              >Projects</a
            >
            <a
              href="#skills"
              class="text-gray-600 hover:text-primary transition-colors"
              >Skills</a
            >
            <a
              href="#contact"
              class="bg-primary hover:bg-[#13a696] text-white px-4 py-2 !rounded-button transition-all duration-300 inline-block text-center"
              >Contact</a
            >
          </div>
        </div>
      </div>
    </nav>
    <!-- Progress Bar -->
    <div class="progress-bar" id="progressBar"></div>
    <!-- Hero Section -->
    <section
      id="hero"
      class="h-screen flex items-center justify-center relative bg-gradient-to-br from-gray-50 to-gray-100 pt-16"
    >
      <div class="container mx-auto px-6 md:px-12 text-center">
        <h2 class="text-2xl md:text-3xl text-gray-600 mb-4">Hi, I'm</h2>
        <h1
          class="text-5xl md:text-7xl font-bold text-gray-800 mb-6 mx-auto"
        >
          John Anderson
        </h1>
        <p class="text-xl md:text-2xl text-gray-600 mt-4 mb-8">
          Frontend Developer & UI/UX Designer
        </p>
        <div class="flex justify-center gap-4 mt-8">
          <a
            href="#projects"
            class="bg-primary hover:bg-[#13a696] text-white font-medium py-3 px-8 !rounded-button transition-all duration-300 shadow-md hover:shadow-lg whitespace-nowrap inline-block"
          >
            View Portfolio
          </a>
          <a
            href="#contact"
            class="bg-white hover:bg-gray-100 text-gray-800 font-medium py-3 px-8 border border-gray-300 !rounded-button transition-all duration-300 shadow-md hover:shadow-lg whitespace-nowrap inline-block"
          >
            Contact Me
          </a>
        </div>
      </div>
</section>
    <!-- About Section -->
    <section id="about" class="py-20 bg-white">
      <div class="container mx-auto px-6 md:px-12">
        <div class="text-center mb-16">
          <h2 class="text-3xl md:text-4xl font-bold text-gray-800 mb-4">
            About Me
          </h2>
          <div class="w-20 h-1 bg-primary mx-auto"></div>
        </div>
        <div class="flex flex-col md:flex-row gap-12 items-center">
          <div class="md:w-1/2">
            <img
              src="img/profile.png"
              alt="John Anderson"
              class="rounded-lg w-full h-auto object-cover object-top"
            />
          </div>
          <div class="md:w-1/2">
            <h3 class="text-2xl font-semibold text-gray-800 mb-4">Who I Am</h3>
            <p class="text-gray-600 mb-6 leading-relaxed">
              I'm a passionate frontend developer and UI/UX designer with over 5
              years of experience creating beautiful, functional, and
              user-centered digital experiences. Based in San Francisco, I
              combine technical expertise with creative problem-solving to build
              websites and applications that people love to use.
            </p>
            <p class="text-gray-600 mb-8 leading-relaxed">
              My approach to design focuses on the perfect balance of form and
              function. I believe that great design should not only look good
              but also solve real problems and create meaningful connections
              with users.
            </p>
            <div class="grid grid-cols-2 gap-6">
              <div>
                <h4 class="font-semibold text-gray-800 mb-2">Skills</h4>
                <ul class="text-gray-600 space-y-2">
                  <li class="flex items-center">
                    <div
                      class="w-5 h-5 mr-2 flex items-center justify-center text-primary"
                    >
                      <i class="ri-check-line"></i>
                    </div>
                    HTML5 & CSS3
                  </li>
                  <li class="flex items-center">
                    <div
                      class="w-5 h-5 mr-2 flex items-center justify-center text-primary"
                    >
                      <i class="ri-check-line"></i>
                    </div>
                    JavaScript (ES6+)
                  </li>
                  <li class="flex items-center">
                    <div
                      class="w-5 h-5 mr-2 flex items-center justify-center text-primary"
                    >
                      <i class="ri-check-line"></i>
                    </div>
                    React & Vue.js
                  </li>
                  <li class="flex items-center">
                    <div
                      class="w-5 h-5 mr-2 flex items-center justify-center text-primary"
                    >
                      <i class="ri-check-line"></i>
                    </div>
                    Tailwind CSS
                  </li>
                </ul>
              </div>
              <div>
                <h4 class="font-semibold text-gray-800 mb-2">Tools</h4>
                <ul class="text-gray-600 space-y-2">
                  <li class="flex items-center">
                    <div
                      class="w-5 h-5 mr-2 flex items-center justify-center text-primary"
                    >
                      <i class="ri-check-line"></i>
                    </div>
                    Figma & Adobe XD
                  </li>
                  <li class="flex items-center">
                    <div
                      class="w-5 h-5 mr-2 flex items-center justify-center text-primary"
                    >
                      <i class="ri-check-line"></i>
                    </div>
                    Git & GitHub
                  </li>
                  <li class="flex items-center">
                    <div
                      class="w-5 h-5 mr-2 flex items-center justify-center text-primary"
                    >
                      <i class="ri-check-line"></i>
                    </div>
                    VS Code
                  </li>
                  <li class="flex items-center">
                    <div
                      class="w-5 h-5 mr-2 flex items-center justify-center text-primary"
                    >
                      <i class="ri-check-line"></i>
                    </div>
                    Webpack & Vite
                  </li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
    <!-- Projects Section -->
    <section id="projects" class="py-20 bg-gray-50">
      <div class="container mx-auto px-6 md:px-12">
        <div class="text-center mb-16">
          <h2 class="text-3xl md:text-4xl font-bold text-gray-800 mb-4">
            My Projects
          </h2>
          <div class="w-20 h-1 bg-primary mx-auto"></div>
          <p class="text-gray-600 mt-4 max-w-2xl mx-auto">
            Here are some of my recent projects that showcase my skills and
            expertise in frontend development and UI/UX design.
          </p>
        </div>
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          <!-- Project 1 -->
          <div
            class="bg-white rounded-lg overflow-hidden shadow-md hover:shadow-xl transition-shadow duration-300 project-card"
          >
            <div class="h-60 overflow-hidden">
              <img
                src="https://readdy.ai/api/search-image?query=modern%20e-commerce%20website%20interface%20with%20clean%20design%2C%20product%20grid%20layout%2C%20shopping%20cart%20functionality%2C%20responsive%20design%20elements%2C%20professional%20UI%2FUX%20design%2C%20high%20quality%20digital%20mockup&width=600&height=400&seq=portfolio2&orientation=landscape"
                alt="E-commerce Website"
                class="w-full h-full object-cover object-top transition-transform duration-500 hover:scale-105"
              />
            </div>
            <div class="p-6">
              <h3 class="text-xl font-semibold text-gray-800 mb-2">
                E-commerce Website
              </h3>
              <p class="text-gray-600 mb-4">
                A fully responsive e-commerce platform with cart functionality,
                user authentication, and payment integration.
              </p>
              <div class="flex flex-wrap gap-2 mb-4">
                <span
                  class="text-xs font-medium bg-blue-100 text-blue-800 px-2.5 py-0.5 rounded"
                  >React</span
                >
                <span
                  class="text-xs font-medium bg-green-100 text-green-800 px-2.5 py-0.5 rounded"
                  >Node.js</span
                >
                <span
                  class="text-xs font-medium bg-purple-100 text-purple-800 px-2.5 py-0.5 rounded"
                  >MongoDB</span
                >
              </div>
              <a
                href="#"
                class="inline-flex items-center text-primary hover:text-blue-700 font-medium"
              >
                View Project
                <div class="w-5 h-5 ml-1 flex items-center justify-center">
                  <i class="ri-arrow-right-line"></i>
                </div>
              </a>
            </div>
          </div>
          <!-- Project 2 -->
          <div
            class="bg-white rounded-lg overflow-hidden shadow-md hover:shadow-xl transition-shadow duration-300 project-card"
          >
            <div class="h-60 overflow-hidden">
              <img
                src="https://readdy.ai/api/search-image?query=mobile%20app%20dashboard%20interface%20with%20data%20visualization%2C%20user%20analytics%2C%20modern%20UI%20design%2C%20dark%20mode%2C%20charts%20and%20graphs%2C%20professional%20mobile%20application%20mockup&width=600&height=400&seq=portfolio3&orientation=landscape"
                alt="Dashboard App"
                class="w-full h-full object-cover object-top transition-transform duration-500 hover:scale-105"
              />
            </div>
            <div class="p-6">
              <h3 class="text-xl font-semibold text-gray-800 mb-2">
                Analytics Dashboard
              </h3>
              <p class="text-gray-600 mb-4">
                An interactive dashboard for visualizing business metrics with
                real-time data updates and customizable widgets.
              </p>
              <div class="flex flex-wrap gap-2 mb-4">
                <span
                  class="text-xs font-medium bg-blue-100 text-blue-800 px-2.5 py-0.5 rounded"
                  >Vue.js</span
                >
                <span
                  class="text-xs font-medium bg-yellow-100 text-yellow-800 px-2.5 py-0.5 rounded"
                  >D3.js</span
                >
                <span
                  class="text-xs font-medium bg-red-100 text-red-800 px-2.5 py-0.5 rounded"
                  >Firebase</span
                >
              </div>
              <a
                href="#"
                class="inline-flex items-center text-primary hover:text-blue-700 font-medium"
              >
                View Project
                <div class="w-5 h-5 ml-1 flex items-center justify-center">
                  <i class="ri-arrow-right-line"></i>
                </div>
              </a>
            </div>
          </div>
          <!-- Project 3 -->
          <div
            class="bg-white rounded-lg overflow-hidden shadow-md hover:shadow-xl transition-shadow duration-300 project-card"
          >
            <div class="h-60 overflow-hidden">
              <img
                src="https://readdy.ai/api/search-image?query=fitness%20mobile%20application%20interface%20with%20workout%20tracking%2C%20progress%20charts%2C%20clean%20minimal%20design%2C%20user%20profile%20page%2C%20exercise%20library%2C%20professional%20mobile%20app%20mockup&width=600&height=400&seq=portfolio4&orientation=landscape"
                alt="Fitness App"
                class="w-full h-full object-cover object-top transition-transform duration-500 hover:scale-105"
              />
            </div>
            <div class="p-6">
              <h3 class="text-xl font-semibold text-gray-800 mb-2">
                Fitness Tracker App
              </h3>
              <p class="text-gray-600 mb-4">
                A mobile app for tracking workouts, setting fitness goals, and
                monitoring progress with personalized recommendations.
              </p>
              <div class="flex flex-wrap gap-2 mb-4">
                <span
                  class="text-xs font-medium bg-blue-100 text-blue-800 px-2.5 py-0.5 rounded"
                  >React Native</span
                >
                <span
                  class="text-xs font-medium bg-green-100 text-green-800 px-2.5 py-0.5 rounded"
                  >Redux</span
                >
                <span
                  class="text-xs font-medium bg-gray-100 text-gray-800 px-2.5 py-0.5 rounded"
                  >GraphQL</span
                >
              </div>
              <a
                href="#"
                class="inline-flex items-center text-primary hover:text-blue-700 font-medium"
              >
                View Project
                <div class="w-5 h-5 ml-1 flex items-center justify-center">
                  <i class="ri-arrow-right-line"></i>
                </div>
              </a>
            </div>
          </div>
        </div>
        <div class="text-center mt-12">
          <a
            href="#"
            class="inline-block bg-white hover:bg-gray-100 text-gray-800 font-medium py-3 px-8 border border-gray-300 !rounded-button transition-all duration-300 shadow-md hover:shadow-lg whitespace-nowrap"
          >
            View All Projects
          </a>
        </div>
      </div>
    </section>
    <!-- Skills Section -->
    <section id="skills" class="py-20 bg-white">
      <div class="container mx-auto px-6 md:px-12">
        <div class="text-center mb-16">
          <h2 class="text-3xl md:text-4xl font-bold text-gray-800 mb-4">
            My Skills
          </h2>
          <div class="w-20 h-1 bg-primary mx-auto"></div>
          <p class="text-gray-600 mt-4 max-w-2xl mx-auto">
            I've developed expertise in various technologies and tools
            throughout my career.
          </p>
        </div>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-12">
          <!-- Technical Skills -->
          <div>
            <h3 class="text-2xl font-semibold text-gray-800 mb-6">
              Technical Skills
            </h3>
            <!-- Skill 1 -->
            <div class="mb-6 skill-card">
              <div class="flex justify-between mb-1">
                <span class="text-base font-medium text-gray-700"
                  >HTML & CSS</span
                >
                <span class="text-sm font-medium text-gray-700">95%</span>
              </div>
              <div class="w-full bg-gray-200 rounded-full h-2.5">
                <div
                  class="bg-primary h-2.5 rounded-full animate-width"
                  style="width: 95%"
                ></div>
              </div>
            </div>
            <!-- Skill 2 -->
            <div class="mb-6">
              <div class="flex justify-between mb-1">
                <span class="text-base font-medium text-gray-700"
                  >JavaScript</span
                >
                <span class="text-sm font-medium text-gray-700">90%</span>
              </div>
              <div class="w-full bg-gray-200 rounded-full h-2.5">
                <div
                  class="bg-primary h-2.5 rounded-full"
                  style="width: 90%"
                ></div>
              </div>
            </div>
            <!-- Skill 3 -->
            <div class="mb-6">
              <div class="flex justify-between mb-1">
                <span class="text-base font-medium text-gray-700">React</span>
                <span class="text-sm font-medium text-gray-700">85%</span>
              </div>
              <div class="w-full bg-gray-200 rounded-full h-2.5">
                <div
                  class="bg-primary h-2.5 rounded-full"
                  style="width: 85%"
                ></div>
              </div>
            </div>
            <!-- Skill 4 -->
            <div class="mb-6">
              <div class="flex justify-between mb-1">
                <span class="text-base font-medium text-gray-700">Vue.js</span>
                <span class="text-sm font-medium text-gray-700">80%</span>
              </div>
              <div class="w-full bg-gray-200 rounded-full h-2.5">
                <div
                  class="bg-primary h-2.5 rounded-full"
                  style="width: 80%"
                ></div>
              </div>
            </div>
            <!-- Skill 5 -->
            <div>
              <div class="flex justify-between mb-1">
                <span class="text-base font-medium text-gray-700">Node.js</span>
                <span class="text-sm font-medium text-gray-700">75%</span>
              </div>
              <div class="w-full bg-gray-200 rounded-full h-2.5">
                <div
                  class="bg-primary h-2.5 rounded-full"
                  style="width: 75%"
                ></div>
              </div>
            </div>
          </div>
          <!-- Design Skills -->
          <div>
            <h3 class="text-2xl font-semibold text-gray-800 mb-6">
              Design Skills
            </h3>
            <!-- Skill 1 -->
            <div class="mb-6">
              <div class="flex justify-between mb-1">
                <span class="text-base font-medium text-gray-700"
                  >UI/UX Design</span
                >
                <span class="text-sm font-medium text-gray-700">90%</span>
              </div>
              <div class="w-full bg-gray-200 rounded-full h-2.5">
                <div
                  class="bg-secondary h-2.5 rounded-full"
                  style="width: 90%"
                ></div>
              </div>
            </div>
            <!-- Skill 2 -->
            <div class="mb-6">
              <div class="flex justify-between mb-1">
                <span class="text-base font-medium text-gray-700">Figma</span>
                <span class="text-sm font-medium text-gray-700">85%</span>
              </div>
              <div class="w-full bg-gray-200 rounded-full h-2.5">
                <div
                  class="bg-secondary h-2.5 rounded-full"
                  style="width: 85%"
                ></div>
              </div>
            </div>
            <!-- Skill 3 -->
            <div class="mb-6">
              <div class="flex justify-between mb-1">
                <span class="text-base font-medium text-gray-700"
                  >Adobe XD</span
                >
                <span class="text-sm font-medium text-gray-700">80%</span>
              </div>
              <div class="w-full bg-gray-200 rounded-full h-2.5">
                <div
                  class="bg-secondary h-2.5 rounded-full"
                  style="width: 80%"
                ></div>
              </div>
            </div>
            <!-- Skill 4 -->
            <div class="mb-6">
              <div class="flex justify-between mb-1">
                <span class="text-base font-medium text-gray-700"
                  >Responsive Design</span
                >
                <span class="text-sm font-medium text-gray-700">95%</span>
              </div>
              <div class="w-full bg-gray-200 rounded-full h-2.5">
                <div
                  class="bg-secondary h-2.5 rounded-full"
                  style="width: 95%"
                ></div>
              </div>
            </div>
            <!-- Skill 5 -->
            <div>
              <div class="flex justify-between mb-1">
                <span class="text-base font-medium text-gray-700"
                  >Prototyping</span
                >
                <span class="text-sm font-medium text-gray-700">85%</span>
              </div>
              <div class="w-full bg-gray-200 rounded-full h-2.5">
                <div
                  class="bg-secondary h-2.5 rounded-full"
                  style="width: 85%"
                ></div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
    <!-- Contact Section -->
    <section id="contact" class="py-20 bg-gray-50">
      <div class="container mx-auto px-6 md:px-12">
        <div class="text-center mb-16">
          <h2 class="text-3xl md:text-4xl font-bold text-gray-800 mb-4">
            Get In Touch
          </h2>
          <div class="w-20 h-1 bg-primary mx-auto"></div>
          <p class="text-gray-600 mt-4 max-w-2xl mx-auto">
            Have a project in mind or want to collaborate? Feel free to reach
            out to me.
          </p>
        </div>
        <div class="flex flex-col md:flex-row gap-12">
          <!-- Contact Form -->
          <div class="md:w-2/3 bg-white rounded-lg shadow-md p-8">
            <h3 class="text-2xl font-semibold text-gray-800 mb-6">
              Send Me a Message
            </h3>
            <form>
              <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                <div>
                  <label
                    for="name"
                    class="block text-sm font-medium text-gray-700 mb-2"
                    >Your Name</label
                  >
                  <input
                    type="text"
                    id="name"
                    class="w-full px-4 py-3 border border-gray-300 rounded focus:ring-2 focus:ring-primary focus:border-primary transition-colors"
                    placeholder="John Doe"
                  />
                </div>
                <div>
                  <label
                    for="email"
                    class="block text-sm font-medium text-gray-700 mb-2"
                    >Your Email</label
                  >
                  <input
                    type="email"
                    id="email"
                    class="w-full px-4 py-3 border border-gray-300 rounded focus:ring-2 focus:ring-primary focus:border-primary transition-colors"
                    placeholder="<EMAIL>"
                  />
                </div>
              </div>
              <div class="mb-6">
                <label
                  for="subject"
                  class="block text-sm font-medium text-gray-700 mb-2"
                  >Subject</label
                >
                <input
                  type="text"
                  id="subject"
                  class="w-full px-4 py-3 border border-gray-300 rounded focus:ring-2 focus:ring-primary focus:border-primary transition-colors"
                  placeholder="Project Inquiry"
                />
              </div>
              <div class="mb-6">
                <label
                  for="message"
                  class="block text-sm font-medium text-gray-700 mb-2"
                  >Message</label
                >
                <textarea
                  id="message"
                  rows="5"
                  class="w-full px-4 py-3 border border-gray-300 rounded focus:ring-2 focus:ring-primary focus:border-primary transition-colors"
                  placeholder="Your message here..."
                ></textarea>
              </div>
              <button
                type="submit"
                class="bg-primary hover:bg-[#13a696] text-white font-medium py-3 px-8 !rounded-button transition-all duration-300 shadow-md hover:shadow-lg whitespace-nowrap"
              >
                Send Message
              </button>
            </form>
          </div>
          <!-- Contact Info -->
          <div class="md:w-1/3">
            <div class="bg-white rounded-lg shadow-md p-8 mb-8">
              <h3 class="text-xl font-semibold text-gray-800 mb-6">
                Contact Information
              </h3>
              <div class="space-y-4">
                <div class="flex items-start">
                  <div
                    class="w-10 h-10 flex items-center justify-center bg-blue-100 rounded-full text-primary mr-4"
                  >
                    <i class="ri-map-pin-line ri-lg"></i>
                  </div>
                  <div>
                    <h4 class="text-sm font-medium text-gray-700">Location</h4>
                    <p class="text-gray-600">San Francisco, California</p>
                  </div>
                </div>
                <div class="flex items-start">
                  <div
                    class="w-10 h-10 flex items-center justify-center bg-blue-100 rounded-full text-primary mr-4"
                  >
                    <i class="ri-mail-line ri-lg"></i>
                  </div>
                  <div>
                    <h4 class="text-sm font-medium text-gray-700">Email</h4>
                    <p class="text-gray-600"><EMAIL></p>
                  </div>
                </div>
                <div class="flex items-start">
                  <div
                    class="w-10 h-10 flex items-center justify-center bg-blue-100 rounded-full text-primary mr-4"
                  >
                    <i class="ri-phone-line ri-lg"></i>
                  </div>
                  <div>
                    <h4 class="text-sm font-medium text-gray-700">Phone</h4>
                    <p class="text-gray-600">+****************</p>
                  </div>
                </div>
              </div>
            </div>
            <div class="bg-white rounded-lg shadow-md p-8">
              <h3 class="text-xl font-semibold text-gray-800 mb-6">
                Follow Me
              </h3>
              <div class="flex space-x-4">
                <a
                  href="#"
                  class="w-10 h-10 flex items-center justify-center bg-gray-100 hover:bg-blue-100 rounded-full text-gray-600 hover:text-primary transition-colors"
                >
                  <i class="ri-github-fill ri-lg"></i>
                </a>
                <a
                  href="#"
                  class="w-10 h-10 flex items-center justify-center bg-gray-100 hover:bg-blue-100 rounded-full text-gray-600 hover:text-primary transition-colors"
                >
                  <i class="ri-linkedin-fill ri-lg"></i>
                </a>
                <a
                  href="#"
                  class="w-10 h-10 flex items-center justify-center bg-gray-100 hover:bg-blue-100 rounded-full text-gray-600 hover:text-primary transition-colors"
                >
                  <i class="ri-twitter-fill ri-lg"></i>
                </a>
                <a
                  href="#"
                  class="w-10 h-10 flex items-center justify-center bg-gray-100 hover:bg-blue-100 rounded-full text-gray-600 hover:text-primary transition-colors"
                >
                  <i class="ri-dribbble-fill ri-lg"></i>
                </a>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
    <!-- Footer -->
    <footer class="bg-gray-800 text-white py-12">
      <div class="container mx-auto px-6 md:px-12">
        <div class="flex flex-col md:flex-row justify-between items-center">
          <div class="mb-6 md:mb-0">
            <a href="#" class="text-2xl font-['Pacifico'] text-white"
              >John Anderson</a
            >
            <p class="text-gray-400 mt-2">
              Frontend Developer & UI/UX Designer
            </p>
          </div>
          <div
            class="flex flex-col md:flex-row md:items-center space-y-4 md:space-y-0 md:space-x-8"
          >
            <a
              href="#hero"
              class="text-gray-300 hover:text-white transition-colors"
              >Home</a
            >
            <a
              href="#about"
              class="text-gray-300 hover:text-white transition-colors"
              >About</a
            >
            <a
              href="#projects"
              class="text-gray-300 hover:text-white transition-colors"
              >Projects</a
            >
            <a
              href="#skills"
              class="text-gray-300 hover:text-white transition-colors"
              >Skills</a
            >
            <a
              href="#contact"
              class="text-gray-300 hover:text-white transition-colors"
              >Contact</a
            >
          </div>
        </div>
        <hr class="border-gray-700 my-8" />
        <div class="text-center text-gray-400">
          <p>© 2025 John Anderson. All rights reserved.</p>
          <p class="mt-2">Last updated: May 27, 2025</p>
        </div>
      </div>
    </footer>
    <!-- Scroll to Top Button -->
    <button
      id="scrollTopBtn"
      class="scroll-top-button fixed bottom-6 right-6 w-12 h-12 bg-primary hover:bg-[#13a696] text-white rounded-full shadow-lg flex items-center justify-center transition-all duration-300 z-50"
    >
      <i class="ri-arrow-up-line ri-lg"></i>
    </button>
    <!-- Scripts -->
    <script id="navigationBehavior">
      document.addEventListener("DOMContentLoaded", function () {
        const mobileMenuBtn = document.getElementById("mobileMenuBtn");
        const mobileMenu = document.getElementById("mobileMenu");
        const mobileMenuIcon = mobileMenuBtn.querySelector("i");
        // Toggle mobile menu
        mobileMenuBtn.addEventListener("click", function () {
          mobileMenu.classList.toggle("hidden");
          const isOpen = !mobileMenu.classList.contains("hidden");
          mobileMenuIcon.className = isOpen
            ? "ri-close-line ri-lg"
            : "ri-menu-line ri-lg";
        });
        // Close mobile menu when clicking menu items
        mobileMenu.querySelectorAll("a").forEach((link) => {
          link.addEventListener("click", function () {
            mobileMenu.classList.add("hidden");
            mobileMenuIcon.className = "ri-menu-line ri-lg";
          });
        });
        // Close mobile menu on window resize
        window.addEventListener("resize", function () {
          if (window.innerWidth >= 768) {
            mobileMenu.classList.add("hidden");
            mobileMenuIcon.className = "ri-menu-line ri-lg";
          }
        });
      });
    </script>
    <script id="scrollBehavior">
      document.addEventListener("DOMContentLoaded", function () {
        // Smooth scroll for anchor links
        document.querySelectorAll('a[href^="#"]').forEach((anchor) => {
          anchor.addEventListener("click", function (e) {
            e.preventDefault();
            const targetId = this.getAttribute("href");
            const targetElement = document.querySelector(targetId);
            if (targetElement) {
              window.scrollTo({
                top: targetElement.offsetTop,
                behavior: "smooth",
              });
            }
          });
        });
        // Intersection Observer for scroll animations
        const animateOnScroll = new IntersectionObserver(
          (entries) => {
            entries.forEach((entry) => {
              if (entry.isIntersecting) {
                entry.target.classList.add("visible");
              }
            });
          },
          {
            threshold: 0.1,
          },
        );
        // Add animation classes and observe elements
        document.querySelectorAll("section").forEach((section) => {
          section.classList.add("animate-fade-up");
          animateOnScroll.observe(section);
        });
        document
          .querySelectorAll(".project-card, .skill-card")
          .forEach((element, index) => {
            element.classList.add("animate-fade-up", "stagger-animation");
            element.style.setProperty("--animation-order", index);
            animateOnScroll.observe(element);
          });
        document.querySelectorAll("img").forEach((img) => {
          img.classList.add("animate-fade-in");
          animateOnScroll.observe(img);
        });
        // Add hover animations to cards and buttons
        document.querySelectorAll(".bg-white.rounded-lg").forEach((card) => {
          card.classList.add("scale-hover");
        });
        // Add nav link hover effect
        document.querySelectorAll(".text-gray-600").forEach((link) => {
          if (link.tagName === "A") {
            link.classList.add("nav-link");
          }
        });
      });
    </script>
    <script id="scrollToTopButton">
      document.addEventListener("DOMContentLoaded", function () {
        const scrollTopBtn = document.getElementById("scrollTopBtn");
        // Show/hide scroll to top button based on scroll position
        window.addEventListener("scroll", function () {
          if (window.pageYOffset > 300) {
            scrollTopBtn.classList.add("visible");
          } else {
            scrollTopBtn.classList.remove("visible");
          }
        });
        // Scroll to top when button is clicked
        scrollTopBtn.addEventListener("click", function () {
          window.scrollTo({
            top: 0,
            behavior: "smooth",
          });
        });
      });
    </script>
    <script id="progressBarScript">
      document.addEventListener("DOMContentLoaded", function() {
        const progressBar = document.getElementById("progressBar");

        window.addEventListener("scroll", function() {
          const windowHeight = document.documentElement.scrollHeight - document.documentElement.clientHeight;
          const scrolled = (window.scrollY / windowHeight) * 100;
          progressBar.style.width = scrolled + "%";
        });
      });
    </script>

  </body>
</html>
