 * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        :root {
            --primary-black: #000000;
            --dodger-blue: #1E90FF;
            --dark-gray: #111111;
            --light-gray: #333333;
        }

        html {
            scroll-behavior: smooth;
        }

        body {
            font-family: 'Arial', sans-serif;
            background-color: var(--primary-black);
            color: white;
            line-height: 1.6;
            overflow-x: hidden;
        }

        /* Navigation */
        .navbar {
            position: fixed;
            top: 0;
            width: 100%;
            background: rgba(0, 0, 0, 0.95);
            backdrop-filter: blur(10px);
            z-index: 1000;
            padding: 1rem 0;
            transition: all 0.3s ease;
        }

        .navbar.scrolled {
            background: rgba(0, 0, 0, 0.98);
            box-shadow: 0 2px 20px rgba(30, 144, 255, 0.1);
        }

        .nav-container {
            max-width: 1200px;
            margin: 0 auto;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 2rem;
        }

        .logo {
            font-size: 1.5rem;
            font-weight: bold;
            color: var(--dodger-blue);
            text-decoration: none;
            transition: all 0.3s ease;
        }

        .logo:hover {
            transform: scale(1.05);
            text-shadow: 0 0 10px var(--dodger-blue);
        }

        .nav-links {
            display: flex;
            list-style: none;
            gap: 2rem;
        }

        .nav-links a {
            color: white;
            text-decoration: none;
            transition: all 0.3s ease;
            position: relative;
            padding: 0.5rem 0;
        }

        .nav-links a::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            width: 0;
            height: 2px;
            background: var(--dodger-blue);
            transition: width 0.3s ease;
        }

        .nav-links a:hover::after {
            width: 100%;
        }

        .nav-links a:hover {
            color: var(--dodger-blue);
        }

        /* Mobile Menu */
        .mobile-menu {
            display: none;
            flex-direction: column;
            cursor: pointer;
            padding: 10px;
            z-index: 1001;
        }

        .mobile-menu span {
            width: 25px;
            height: 3px;
            background: white;
            margin: 3px 0;
            transition: all 0.3s ease;
            transform-origin: center;
        }

        .mobile-menu.active span:nth-child(1) {
            transform: rotate(45deg) translate(6px, 6px);
        }

        .mobile-menu.active span:nth-child(2) {
            opacity: 0;
        }

        .mobile-menu.active span:nth-child(3) {
            transform: rotate(-45deg) translate(6px, -6px);
        }

        .mobile-nav-dropdown {
            position: absolute;
            top: 100%;
            right: 0;
            background: rgba(0, 0, 0, 0.98);
            backdrop-filter: blur(10px);
            border: 2px solid var(--dodger-blue);
            border-radius: 15px;
            padding: 2rem;
            min-width: 200px;
            opacity: 0;
            visibility: hidden;
            transform: translateY(-20px);
            transition: all 0.3s ease;
            box-shadow: 0 10px 30px rgba(30, 144, 255, 0.2);
        }

        .mobile-nav-dropdown.active {
            opacity: 1;
            visibility: visible;
            transform: translateY(0);
        }

        .mobile-nav-dropdown .nav-links {
            display: flex;
            flex-direction: column;
            gap: 1.5rem;
        }

        .mobile-nav-dropdown .nav-links a {
            color: white;
            text-decoration: none;
            padding: 0.8rem 0;
            border-bottom: 1px solid var(--light-gray);
            transition: all 0.3s ease;
            text-align: right;
        }

        .mobile-nav-dropdown .nav-links a:last-child {
            border-bottom: none;
        }

        .mobile-nav-dropdown .nav-links a:hover {
            color: var(--dodger-blue);
            transform: translateX(-10px);
        }

        /* Hero Section */
        .hero {
            height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            text-align: center;
            background: linear-gradient(135deg, var(--primary-black) 0%, var(--dark-gray) 100%);
            position: relative;
            overflow: hidden;
        }

        .hero::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: radial-gradient(circle at 50% 50%, rgba(30, 144, 255, 0.1) 0%, transparent 70%);
            z-index: 1;
        }

        .hero-content {
            z-index: 2;
            max-width: 800px;
            padding: 0 2rem;
            animation: fadeInUp 1s ease-out;
        }

        .hero h1 {
            font-size: clamp(3rem, 8vw, 6rem);
            font-weight: 700;
            margin-bottom: 1rem;
            background: linear-gradient(135deg, white 0%, var(--dodger-blue) 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .hero .subtitle {
            font-size: clamp(1.2rem, 3vw, 2rem);
            margin-bottom: 2rem;
            color: #ccc;
        }

        .hero .description {
            font-size: 1.2rem;
            margin-bottom: 3rem;
            opacity: 0.8;
        }

        .cta-button {
            display: inline-block;
            padding: 1rem 2rem;
            background: var(--dodger-blue);
            color: white;
            text-decoration: none;
            border-radius: 50px;
            transition: all 0.3s ease;
            font-weight: 600;
            position: relative;
            overflow: hidden;
        }

        .cta-button::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s;
        }

        .cta-button:hover::before {
            left: 100%;
        }

        .cta-button:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 30px rgba(30, 144, 255, 0.4);
        }

        /* Sections */
        .section {
            padding: 6rem 0;
            max-width: 1200px;
            margin: 0 auto;
            padding-left: 2rem;
            padding-right: 2rem;
        }

        .section-title {
            font-size: 3rem;
            text-align: center;
            margin-bottom: 3rem;
            color: var(--dodger-blue);
            position: relative;
        }

        .section-title::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 80px;
            height: 3px;
            background: var(--dodger-blue);
        }

        /* About Section */
        .about-content {
            display: grid;
            grid-template-columns: 1fr 2fr;
            gap: 4rem;
            align-items: center;
        }

        .about-image {
            width: 100%;
            max-width: 300px;
            height: 300px;
            border-radius: 50%;
            background: linear-gradient(135deg, var(--dodger-blue), var(--light-gray));
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 4rem;
            color: white;
            margin: 0 auto;
            transition: all 0.3s ease;
        }

        .about-image:hover {
            transform: scale(1.05);
            box-shadow: 0 20px 40px rgba(30, 144, 255, 0.3);
        }

        .about-text {
            font-size: 1.1rem;
            line-height: 1.8;
        }

        .skills {
            display: flex;
            flex-wrap: wrap;
            gap: 1rem;
            margin-top: 2rem;
        }

        .skill-tag {
            background: var(--light-gray);
            padding: 0.5rem 1rem;
            border-radius: 25px;
            font-size: 0.9rem;
            transition: all 0.3s ease;
            border: 2px solid transparent;
        }

        .skill-tag:hover {
            background: var(--dodger-blue);
            transform: translateY(-2px);
            border-color: var(--dodger-blue);
        }

        /* Projects Section */
        .projects-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 2rem;
            margin-top: 3rem;
        }

        .project-card {
            background: var(--dark-gray);
            border-radius: 15px;
            overflow: hidden;
            transition: all 0.3s ease;
            border: 2px solid transparent;
            position: relative;
        }

        .project-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, var(--dodger-blue) 0%, transparent 50%);
            opacity: 0;
            transition: opacity 0.3s ease;
            z-index: 1;
        }

        .project-card:hover::before {
            opacity: 0.1;
        }

        .project-card:hover {
            transform: translateY(-10px);
            border-color: var(--dodger-blue);
            box-shadow: 0 20px 40px rgba(30, 144, 255, 0.2);
        }

        .project-image {
            height: 200px;
            background: linear-gradient(135deg, var(--light-gray), var(--dodger-blue));
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 3rem;
            color: white;
            position: relative;
            z-index: 2;
        }

        .project-content {
            padding: 1.5rem;
            position: relative;
            z-index: 2;
        }

        .project-title {
            font-size: 1.5rem;
            margin-bottom: 1rem;
            color: var(--dodger-blue);
        }

        .project-description {
            margin-bottom: 1.5rem;
            opacity: 0.9;
        }

        .project-tech {
            display: flex;
            flex-wrap: wrap;
            gap: 0.5rem;
            margin-bottom: 1.5rem;
        }

        .tech-tag {
            background: var(--primary-black);
            padding: 0.3rem 0.8rem;
            border-radius: 15px;
            font-size: 0.8rem;
            color: var(--dodger-blue);
            border: 1px solid var(--dodger-blue);
        }

        .project-links {
            display: flex;
            gap: 1rem;
        }

        .project-link {
            color: var(--dodger-blue);
            text-decoration: none;
            padding: 0.5rem 1rem;
            border: 2px solid var(--dodger-blue);
            border-radius: 25px;
            transition: all 0.3s ease;
            display: inline-block;
        }

        .project-link:hover {
            background: var(--dodger-blue);
            color: white;
            transform: translateY(-2px);
        }

        /* Contact Section */
        .contact-content {
            text-align: center;
            max-width: 800px;
            margin: 0 auto;
        }

        .contact-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 4rem;
            margin-top: 3rem;
            align-items: start;
        }

        .contact-info {
            display: flex;
            flex-direction: column;
            gap: 2rem;
        }

        .contact-item {
            display: flex;
            align-items: center;
            gap: 1rem;
            transition: all 0.3s ease;
            justify-content: flex-start;
        }

        .contact-item:hover {
            color: var(--dodger-blue);
            transform: translateY(-3px);
        }

        .contact-icon {
            width: 40px;
            height: 40px;
            background: var(--dodger-blue);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.2rem;
            flex-shrink: 0;
        }

        /* Contact Form */
        .contact-form {
            background: var(--dark-gray);
            padding: 2rem;
            border-radius: 15px;
            border: 2px solid transparent;
            transition: all 0.3s ease;
        }

        .contact-form:hover {
            border-color: var(--dodger-blue);
            box-shadow: 0 10px 30px rgba(30, 144, 255, 0.1);
        }

        .form-group {
            margin-bottom: 1.5rem;
            text-align: left;
        }

        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
            color: var(--dodger-blue);
            font-weight: 600;
        }

        .form-group input,
        .form-group textarea {
            width: 100%;
            padding: 1rem;
            background: var(--primary-black);
            border: 2px solid var(--light-gray);
            border-radius: 8px;
            color: white;
            font-size: 1rem;
            transition: all 0.3s ease;
            font-family: inherit;
        }

        .form-group input:focus,
        .form-group textarea:focus {
            outline: none;
            border-color: var(--dodger-blue);
            box-shadow: 0 0 0 3px rgba(30, 144, 255, 0.1);
            transform: translateY(-2px);
        }

        .form-group textarea {
            resize: vertical;
            min-height: 120px;
        }

        .form-group input::placeholder,
        .form-group textarea::placeholder {
            color: #666;
        }

        .submit-btn {
            width: 100%;
            padding: 1rem 2rem;
            background: var(--dodger-blue);
            color: white;
            border: none;
            border-radius: 8px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .submit-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s;
        }

        .submit-btn:hover::before {
            left: 100%;
        }

        .submit-btn:hover {
            background: #0080ff;
            transform: translateY(-2px);
            box-shadow: 0 10px 30px rgba(30, 144, 255, 0.4);
        }

        .submit-btn:active {
            transform: translateY(0);
        }

        .form-message {
            margin-top: 1rem;
            padding: 1rem;
            border-radius: 8px;
            text-align: center;
            font-weight: 600;
            opacity: 0;
            transform: translateY(10px);
            transition: all 0.3s ease;
        }

        .form-message.show {
            opacity: 1;
            transform: translateY(0);
        }

        .form-message.success {
            background: rgba(40, 167, 69, 0.2);
            color: #28a745;
            border: 2px solid #28a745;
        }

        .form-message.error {
            background: rgba(220, 53, 69, 0.2);
            color: #dc3545;
            border: 2px solid #dc3545;
        }

        /* Footer */
        .footer {
            background: var(--dark-gray);
            text-align: center;
            padding: 2rem;
            border-top: 2px solid var(--dodger-blue);
        }

        /* Animations */
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(50px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .fade-in {
            opacity: 0;
            transform: translateY(30px);
            transition: all 0.6s ease;
        }

        .fade-in.visible {
            opacity: 1;
            transform: translateY(0);
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .nav-links {
                display: none;
            }

            .mobile-menu {
                display: flex;
            }

            .nav-container {
                position: relative;
            }

            .about-content {
                grid-template-columns: 1fr;
                text-align: center;
            }

            .projects-grid {
                grid-template-columns: 1fr;
            }

            .contact-grid {
                grid-template-columns: 1fr;
                gap: 3rem;
            }

            .contact-info {
                text-align: center;
            }

            .contact-item {
                justify-content: center;
            }

            .section {
                padding: 4rem 1rem;
            }

            .hero-content {
                padding: 0 1rem;
            }
        }

        @media (max-width: 480px) {
            .hero h1 {
                font-size: 2.5rem;
            }

            .section-title {
                font-size: 2rem;
            }

            .nav-container {
                padding: 0 1rem;
            }
        }